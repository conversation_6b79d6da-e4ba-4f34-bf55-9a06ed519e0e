import { CommerceProductBase } from '@/models';

/**
 * Interface for AB price data structure
 */
interface ABPriceData {
  group_type: string;
  tier_prices?: Array<{
    extension_attributes?: { website_id: number };
    value: number;
    qty: number;
    customer_group_id: string;
  }>;
  zone_prices?: Array<{
    customer_group_id: string;
    qty: number;
    value: number;
    extension_attributes?: { zone_code: string };
  }>;
  country_prices?: Array<{
    customer_group_id: string;
    qty: number;
    value: number;
    extension_attributes?: { country_code: string };
  }>;
}

/**
 * Parse ABPrices field to extract AB test data
 */
export const parseABPrices = (abPricesField?: { value?: string }): ABPriceData[] => {
  if (!abPricesField?.value) {
    return [];
  }

  try {
    return JSON.parse(abPricesField.value) as ABPriceData[];
  } catch (error) {
    console.warn('Failed to parse ABPrices field:', error);
    return [];
  }
};

/**
 * Check if a product has AB test configuration and get the variant
 */
export const getProductABTestInfo = (product: CommerceProductBase): { isEligible: boolean; variant: string | null } => {
  const abPricesData = parseABPrices(product.ABPrices);

  if (abPricesData.length === 0) {
    return { isEligible: false, variant: null };
  }

  // Find the first entry with a group_type (should be "A" or "B")
  const abTestEntry = abPricesData.find((entry) => entry.group_type);

  if (!abTestEntry) {
    return { isEligible: false, variant: null };
  }

  return {
    isEligible: true,
    variant: abTestEntry.group_type,
  };
};

/**
 * Generate test name following the naming convention
 * Format: "team:sku:version" (e.g., "spx:EL106:v01")
 */
export const generateTestName = (team: string, sku: string, version: string): string => {
  return `${team}:${sku}:${version}`;
};

/**
 * Get variant for a product from its ABPrices data
 * This replaces the old cookie-based variant assignment
 */
export const getProductVariant = (product: CommerceProductBase): string | null => {
  const { isEligible, variant } = getProductABTestInfo(product);
  return isEligible ? variant : null;
};

/**
 * Generate the full variant name following the naming convention
 * Format: testName:variant (e.g., "spx:EL106:v01:verA")
 */
export const generateVariantName = (testName: string, variant: string): string => {
  return `${testName}:${variant}`;
};

/**
 * Check if a product is eligible for A/B testing based on ABPrices field
 */
export const isProductEligibleForTest = (product: CommerceProductBase): boolean => {
  const { isEligible } = getProductABTestInfo(product);
  return isEligible;
};
