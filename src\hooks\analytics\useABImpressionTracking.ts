import { CommerceProductBase } from '@/models';
import { generateTestName, generateVariantName, getProductVariant, isProductEligibleForTest } from '@/utils/abTesting';
import { ABTestEventNames, emitEvent, EventTypes } from '@pmi/analytics-layer';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import useSpxHeadlessSettings from '../Sitecore/useSpxHeadlessSettings';

/**
 * Interface for A/B impression event data matching Adobe Analytics format
 */
interface ABImpressionEventData {
  ab: {
    testName: string;
    experienceName: string;
    status: 'member' | 'nonmember' | 'unknown';
  };
}

/**
 * Hook for tracking A/B test impressions on product pages
 */
export const useABImpressionTracking = () => {
  const settings = useSpxHeadlessSettings();
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { data: activeMemberships } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });

  /**
   * Determine user membership status
   */
  const getMembershipStatus = (): 'member' | 'nonmember' | 'unknown' => {
    if (!isAuthenticated) {
      return 'unknown';
    }

    if (activeMemberships?.hasActiveMembership) {
      return 'member';
    }

    return 'nonmember';
  };

  /**
   * Track A/B impression event for a product
   */
  const trackABImpression = (product: CommerceProductBase) => {
    try {
      // Check if A/B testing is configured in Sitecore settings
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig) {
        return;
      }

      // Get product SKU
      const productSku = product?.ExternalID?.value as string;
      if (!productSku) {
        console.warn('Product SKU not available for A/B impression tracking');
        return;
      }

      // Check if this product is eligible for A/B testing based on ABPrices field
      if (!isProductEligibleForTest(product)) {
        return;
      }

      // Get variant from product's ABPrices data
      const variant = getProductVariant(product);
      if (!variant) {
        console.warn('Failed to get A/B test variant from product data');
        return;
      }

      // Build the test name dynamically from Sitecore settings
      const testName = generateTestName(abTestConfig.team, productSku, abTestConfig.version);

      // Generate the experience name (full variant name)
      const experienceName = generateVariantName(testName, variant);

      // Get membership status
      const membershipStatus = getMembershipStatus();

      // Prepare event data in Adobe Analytics format
      const eventData: ABImpressionEventData = {
        ab: {
          testName,
          experienceName,
          status: membershipStatus,
        },
      };

      // Emit the A/B impression event
      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABImpression,
        eventData,
      });

      console.log('A/B impression tracked:', {
        testName,
        experienceName,
        productSku,
        variant,
      });
    } catch (error) {
      console.error('Failed to track A/B impression:', error);
    }
  };

  /**
   * Track A/B click event (for future use)
   */
  const trackABClick = (product: CommerceProductBase, clickTarget: string) => {
    try {
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig) {
        return;
      }

      const productSku = product?.ExternalID?.value as string;
      if (!productSku || !isProductEligibleForTest(product)) {
        return;
      }

      const variant = getProductVariant(product);
      if (!variant) {
        return;
      }

      // Build the test name dynamically from Sitecore settings
      const testName = generateTestName(abTestConfig.team, productSku, abTestConfig.version);
      const experienceName = generateVariantName(testName, variant);

      // Get membership status for the click event
      const membershipStatus = getMembershipStatus();

      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABClick,
        eventData: {
          ab: {
            testName,
            experienceName,
            abCTA: clickTarget,
            status: membershipStatus,
          },
        },
      });
    } catch (error) {
      console.error('Failed to track A/B click:', error);
    }
  };

  return {
    trackABImpression,
    trackABClick,
  };
};
