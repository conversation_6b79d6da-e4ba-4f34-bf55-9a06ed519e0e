import { MissingDataSource } from '@/components/common';
import { useIsExperienceEditor } from '@/hooks';
import {
  getProductPricing,
  getProductType,
  isMembershipProduct,
  isStudentMembershipProduct,
} from '@/utils/Product/product';
import { Card } from '@pmi/catalyst-card';
import { Separator } from '@pmi/catalyst-separator';
import { cn } from '@pmi/catalyst-utils';
import {
  ChapterMembership,
  Course,
  useGetActiveMembershipsQuery,
  useGetCoursesQuery,
  useGetUserQuery,
} from '@pmi/www-shared/store';
import React, { useCallback, useMemo } from 'react';
import AlreadyChapterMemberLockup from '../components/alreadyChapterMemberLockup';
import { ChapterPriceLockup } from '../components/chapterPriceLockup';
import DonationLockup from '../components/donationLockup';
import MembershipProductPriceLockup from '../components/membershipProductPriceLockup';
import CourseStatusLockup from '../components/purchasedCourseDisplay';
import SinglePriceLockup from '../components/singlePriceLockup';
import StandardPriceLockup from '../components/standardPriceLockup';
import StudentMemberPriceLockup from '../components/studentMemberPriceLockup';
import SubscriptionProductLockup from '../components/subscriptionProductLockup';
import { usePriceLookupContext } from '../context';
import { PriceLockupProps } from '../models';
import { PriceLockupLoadingView } from './loadingView';

const DesktopView: React.FC<PriceLockupProps> = ({ isSticky, ...props }) => {
  const { product, isAuthenticated } = usePriceLookupContext();
  const { isLoading: userDataLoading } = useGetUserQuery(); // need this because there a call in the shared package getting user data and we need to wait for it
  const sku = product?.ExternalID?.value as string;
  const isSubscriptionProduct = product?.is_subscription_product?.value;
  const { data: activeMemberships, isLoading: activeMembershipLoading } = useGetActiveMembershipsQuery(null, {
    skip: !isAuthenticated,
  });
  const { isLoading: coursesLoading, data: userCourses } = useGetCoursesQuery(null, { skip: !isAuthenticated });
  const { memberPrice, regularPrice, currencyCode, currencySymbol, studentMemberPrice } = getProductPricing(product);
  const isStudentMemberProduct = isStudentMembershipProduct(product);
  const productType = getProductType(product);
  const isMembership = isMembershipProduct(product);
  const isExperienceEditor = useIsExperienceEditor();
  const isLoading = userDataLoading || activeMembershipLoading || coursesLoading;
  const isMember = !!activeMemberships?.hasActiveMembership;
  const findActiveChapterCondition = useCallback(
    () =>
      activeMemberships?.chapterMemberships?.length > 0 &&
      activeMemberships?.chapterMemberships?.some((cm) => cm.sku === sku && cm.autoRenewStatus === 'OptIn'),
    [activeMemberships?.chapterMemberships, sku],
  );
  let activeChapter: ChapterMembership;

  const showCurrencyCode = (code: string) => {
    return ['USD'].includes(code.toUpperCase());
  };

  if (findActiveChapterCondition()) {
    activeChapter = activeMemberships?.chapterMemberships.find((cm) => cm.sku === sku);
  }

  const hasPurchasedProduct = useMemo(() => {
    return false;
  }, []);

  const purchasedCourse = useMemo(() => {
    return (userCourses as Course[])?.find((c) => c?.courseId === sku);
  }, [userCourses, sku]);

  const renderMap = useMemo(
    () => ({
      standardPriceLockup: (
        <StandardPriceLockup
          memberPrice={memberPrice}
          regularPrice={regularPrice}
          currencyCode={showCurrencyCode(currencyCode) ? currencyCode : null}
          currencySymbol={currencySymbol}
          isMember={isMember}
          hasPurchasedProduct={hasPurchasedProduct}
          fields={product}
          sku={sku}
          isSticky={isSticky}
          learnMoreAboutMembershipLink={props?.fields?.learnMoreAboutMembershipLink?.value?.href}
          learnMoreAboutMembershipLinkText={props?.fields?.learnMoreAboutMembershipLink?.value?.text}
        />
      ),
      singlePriceLockup: (
        <SinglePriceLockup
          regularPrice={regularPrice}
          currencyCode={showCurrencyCode(currencyCode) ? currencyCode : null}
          currencySymbol={currencySymbol}
          isSticky={isSticky}
          sku={sku}
          fields={product}
        />
      ),
      subscriptionPriceLockup: (
        <SubscriptionProductLockup
          memberPrice={memberPrice}
          regularPrice={regularPrice}
          isMember={isMember}
          isSticky={isSticky}
          currencyCode={showCurrencyCode(currencyCode) ? currencyCode : null}
          currencySymbol={currencySymbol as string}
          sku={sku}
          learnMoreAboutMembershipLink={props?.fields?.learnMoreAboutMembershipLink?.value?.href}
          learnMoreAboutMembershipLinkText={props?.fields?.learnMoreAboutMembershipLink?.value?.text}
        />
      ),
      alreadyChapterMemberLockup: <AlreadyChapterMemberLockup activeChapter={activeChapter} />,
      membershipProductPriceLockup: <MembershipProductPriceLockup isSticky={isSticky} />,
      studentMembershipLockup: <StudentMemberPriceLockup {...props} isSticky={isSticky} />,
      donationLockup: <DonationLockup currencySymbol={currencySymbol} />,
      purchasedCourseDisplay: <CourseStatusLockup course={purchasedCourse} />,
      chapterPriceLockup: (
        <ChapterPriceLockup
          studentPrice={studentMemberPrice}
          isSticky={isSticky}
          learnMoreAboutMembershipLink={props?.fields?.learnMoreAboutMembershipLink?.value?.href}
        />
      ),
    }),
    [
      memberPrice,
      regularPrice,
      currencySymbol,
      isMember,
      hasPurchasedProduct,
      product,
      sku,
      isSticky,
      activeChapter,
      props,
      purchasedCourse,
      studentMemberPrice,
    ],
  );

  const renderPriceLockup = () => {
    if (productType === 'Donation') {
      return renderMap.donationLockup;
    }
    if (findActiveChapterCondition()) {
      return renderMap.alreadyChapterMemberLockup;
    }
    if (productType === 'Chapter Membership') {
      return renderMap.chapterPriceLockup;
    }
    if (purchasedCourse) {
      return renderMap.purchasedCourseDisplay;
    }
    if (isMembership) {
      if (isStudentMemberProduct) {
        return renderMap.studentMembershipLockup;
      }
      return renderMap.membershipProductPriceLockup;
    }
    if (isSubscriptionProduct) {
      return renderMap.subscriptionPriceLockup;
    }
    if (memberPrice != null) {
      return renderMap.standardPriceLockup;
    }
    if (memberPrice == null && regularPrice != null) {
      return renderMap.singlePriceLockup;
    }
    return null;
  };

  if (memberPrice == null && regularPrice == null) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  if (isLoading) {
    if (isSticky) {
      return null;
    }
    return <PriceLockupLoadingView />;
  }
  console.log('product', product);

  return (
    <Card
      variant="ghost"
      className={cn('grid grid-cols-4 lg:grid-cols-6 w-full rounded-none lg:gap-x-[--scale-24]', {
        'items-center flex justify-end lg:gap-x-[--scale-32]': isSticky,
      })}
    >
      {!isSticky && <Separator className="col-span-4 lg:col-span-6 my-[--scale-24] bg-[--fill-off-black-darkest]" />}
      {renderPriceLockup()}
    </Card>
  );
};

export default DesktopView;
