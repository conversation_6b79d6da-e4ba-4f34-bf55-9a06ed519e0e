import { Field, <PERSON>Field, LinkField, RichTextField, TextField } from '@sitecore-jss/sitecore-jss-react';

export interface PageMetaData {
  HtmlTitle?: TextField;
  ExcludeFromExternalSearch?: Field<boolean>;
}

export interface HeroImages {
  HeroImage?: ImageField;
  HeroBackgroundImage?: ImageField;
  HeroBackgroundImageMobile?: ImageField;
  UseDarkThemeForHero?: Field<boolean>;
}

export interface CommerceProductBase extends PageMetaData, HeroImages {
  id: string;
  Name?: TextField;
  ExternalID?: TextField;
  Title?: TextField;
  ShortTitle?: TextField;
  Abstract?: TextField;
  ShortAbstract?: TextField;
  Text?: RichTextField;
  ShortDescription?: TextField;
  FullDescription?: TextField;
  IsInStock?: Field<boolean>;
  is_membership?: Field<boolean>;
  is_subscription_product?: Field<boolean>;
  disabled_Product_URL?: Field<boolean>;
  IsFeatured?: Field<boolean>;
  IsDeleted?: Field<boolean>;
  GlobalPrice?: Field<number>;
  GroupPrices?: Field<string>;
  GroupPricesEx?: NameValuePair[];
  CanonicalUrl?: LinkField;
  fulfillment_provider?: FulfillmentProvider;
  ProductStore?: ProductStore;
  ProductKind?: ProductKind;
  ProductType?: ProductType[];
  OverviewDescription?: RichTextField;
  BundleProductOptions?: Field<string>;
  subscription_frequency: SubscriptionFrequency;
  included_in_membership?: Field<boolean>;
  level_3_hierarchy: ProductHierarchy[];
  PromoBadges?: PromoBadge[];
  CountryPricesEx?: CountryPrice[];
  RenewalPrice?: Field<number>;
  ABPrices?: Field<string>;
}

interface Price {
  customer_group: string;
  price: number;
  renewal_price: number;
}

interface CountryPrice {
  country_code: string;
  prices: Price[];
}

export interface PromoBadge {
  id?: string;
  name?: string;
  displayName?: string;
  fields?: {
    id?: string;
    Color?: Field<string>;
    Variant?: Field<string>;
    BadgeIcon?: Field<string>;
    Value?: TextField;
  };
}

export interface BundleProductOption {
  option_id: number;
  title: string;
  product_links?: ProductLink[];
}

export interface ProductLink {
  id: string;
  sku: string;
}

export interface DigitalProductBase extends CommerceProductBase {
  pdus?: TextField;
  strategic_business_pdus?: TextField;
  leadership_pdus?: TextField;
  technical_pdus?: TextField;
  ceu_credit_value?: TextField;
  length_of_course?: TextField;
  level_of_course?: LevelOfCourse;
  language_format?: LanguageFormat[];
  AssociatedCertifications?: TextField;
  Byline?: TextField;
}

export interface DigitalProductItem extends DigitalProductBase {
  details?: TextField;
}

export interface ElearningItem extends DigitalProductBase {
  format?: TextField;
  pmi_elearning_provider?: PmiElearningProvider;
  lms_code?: TextField;
  who_should_attend?: RichTextField;
  OverviewLanguages?: Field<string>;
  OverviewLanguagesEx?: NameValuePair[];
}

export interface DonationProductItem extends CommerceProductBase {
  donation_fixed_amount?: TextField;
  donation_max_amount?: Field<number>;
  experius_donation_min_amount?: Field<number>;
}

export interface MembershipProductItem extends CommerceProductBase {
  opt_in_for_auto_renew?: Field<boolean>;
  validation?: Field<boolean>;
  is_studentmembership?: Field<boolean>;
}

export interface ChapterMembershipProductItem extends CommerceProductBase {
  region?: CharterItem;
  charter_status?: CharterItem;
  charter_year?: TextField;
  chapter_email?: TextField;
  chapter_phone?: TextField;
  url?: TextField;
}

export interface CharterItem {
  id?: string;
  name?: string;
  displayName?: string;
  fields?: {
    Label?: TextField;
  };
}

export interface ProductKind {
  id?: string;
  name?: string;
  displayName?: string;
}
export interface ProductType {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface LanguageFormat {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface LevelOfCourse {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface FulfillmentProvider {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface PmiElearningProvider {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface ProductStore {
  id?: string;
  url?: string;
  name?: string;
  displayName?: string;
  fields?: {
    DefaultCartLink?: TextField;
    IsDefaultStore?: Field<boolean>;
    IsStoreEnabled?: Field<boolean>;
    IsTieredStore?: Field<boolean>;
    LocalCurrencies?: ProductStoreCurrency[];
    StoreCountries?: ProductStoreCountry[];
    StoreCurrency?: ProductStoreCurrency;
    StoreId?: TextField;
    StoreIdentifier?: TextField;
    StoreName?: TextField;
    WebsiteId?: TextField;
    StoreLanguage?: TextField;
    ExternalID?: TextField;
  };
}

export interface ProductStoreCurrency {
  id?: string;
  name?: string;
  displayName?: string;
  fields?: {
    CurrencyCode?: TextField;
    CurrencyName?: TextField;
    CurrencySymbol?: TextField;
    CurrencyDecimalPlaces?: Field<number>;
  };
}

export interface ProductStoreCountry {
  id?: string;
  name?: string;
  displayName?: string;
  fields?: { Code?: TextField; Code3?: TextField; Name?: TextField };
}

export interface NameValuePair {
  name?: string;
  value?: any;
}

export interface SubscriptionFrequency {
  id?: string;
  name?: string;
  displayName?: string;
}

export interface ProductHierarchy {
  id?: string;
  name?: string;
  displayName?: string;
}
